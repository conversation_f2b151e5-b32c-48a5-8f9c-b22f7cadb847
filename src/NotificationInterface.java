
import javax.swing.*;
import java.awt.*;
import java.awt.event.*;

public class NotificationInterface extends J<PERSON>rame {
    public NotificationInterface() {
        setTitle("Notification Interface");
        setSize(350, 400);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY, 2));
        mainPanel.setBackground(Color.WHITE);

        JLabel titleLabel = new JLabel("Notification Interface", SwingConstants.CENTER);
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(10,0,10,0));
        mainPanel.add(titleLabel);

        // Add three notification panels with specific names
        mainPanel.add(createNotificationPanel("Alert"));
        mainPanel.add(Box.createVerticalStrut(15));
        mainPanel.add(createNotificationPanel("LowStock"));
        mainPanel.add(Box.createVerticalStrut(15));
        mainPanel.add(createNotificationPanel("KPI"));
        mainPanel.add(Box.createVerticalStrut(15));

        JLabel moreLabel = new JLabel("More Notification");
        moreLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        mainPanel.add(moreLabel);

        JTextArea moreTextArea = new JTextArea(4, 25);
        moreTextArea.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
        moreTextArea.setEditable(false);
        mainPanel.add(moreTextArea);

        add(mainPanel);
        setVisible(true);
    }

    private JPanel createNotificationPanel(String message) {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.X_AXIS));
        panel.setBackground(Color.WHITE);

        JLabel label = new JLabel(message);
        JComboBox<String> comboBox = new JComboBox<>(new String[]{"Dismiss", "View"});
        JButton actionButton = new JButton("Select");

        panel.add(label);
        panel.add(Box.createHorizontalStrut(20));
        panel.add(comboBox);
        panel.add(Box.createHorizontalStrut(10));
        panel.add(actionButton);

        // Action on button click
        actionButton.addActionListener(e -> {
            String choice = (String) comboBox.getSelectedItem();
            if ("Dismiss".equals(choice)) {
                panel.setVisible(false);
            } else if ("View".equals(choice)) {
                JOptionPane.showMessageDialog(panel, "Viewing: " + message);
            }
        });

        return panel;
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(NotificationInterface::new);
    }
}

