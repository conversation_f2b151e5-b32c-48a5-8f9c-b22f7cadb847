import javax.swing.*;
import java.awt.*;

public class NotificationInterface_1 extends J<PERSON>rame {
    private final JComboBox<String> alertCombo;
    private final JComboBox<String> lowStockCombo;
    private final JComboBox<String> kpiCombo;
    private final JTextArea moreArea;

    public NotificationInterface_1() {
        super("Notification Interface");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(360, 420);
        setLocationRelativeTo(null);

        // Root panel with padding + GridBag for tidy alignment
        JPanel content = new JPanel(new GridBagLayout());
        content.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        GridBagConstraints gc = new GridBagConstraints();
        gc.insets = new Insets(6, 6, 6, 6);
        gc.fill = GridBagConstraints.HORIZONTAL;
        gc.weightx = 1;

        // Header
        JLabel header = new JLabel("Notification Interface", SwingConstants.CENTER);
        header.setFont(header.getFont().deriveFont(Font.BOLD, 14f));
        gc.gridx = 0; gc.gridy = 0; gc.gridwidth = 3;
        content.add(header, gc);

        // Combo options
        String[] options = {"Dismiss", "Open", "Snooze 5 min", "Snooze 1 hour", "Mute"};

        alertCombo = new JComboBox<>(options);
        lowStockCombo = new JComboBox<>(options);
        kpiCombo = new JComboBox<>(options);

        // Rows
        addRow(content, gc, 1, "Alert",    alertCombo);
        addRow(content, gc, 2, "LowStock", lowStockCombo);
        addRow(content, gc, 3, "KPI",      kpiCombo);

        // “More Notification” label
        JLabel moreLbl = new JLabel("More Notification", SwingConstants.CENTER);
        gc.gridx = 0; gc.gridy = 4; gc.gridwidth = 3;
        content.add(moreLbl, gc);

        // Text area
        moreArea = new JTextArea(8, 20);
        moreArea.setLineWrap(true);
        moreArea.setWrapStyleWord(true);
        JScrollPane scroll = new JScrollPane(moreArea);
        gc.gridx = 0; gc.gridy = 5; gc.gridwidth = 3;
        gc.weighty = 1; gc.fill = GridBagConstraints.BOTH;
        content.add(scroll, gc);

        setContentPane(content);
    }

    private void addRow(JPanel panel, GridBagConstraints base, int row, String labelText, JComboBox<String> combo) {
        // Label
        GridBagConstraints c = (GridBagConstraints) base.clone();
        c.gridx = 0; c.gridy = row; c.gridwidth = 1; c.weightx = 0; c.fill = GridBagConstraints.NONE;
        panel.add(new JLabel(labelText), c);

        // Combo
        c = (GridBagConstraints) base.clone();
        c.gridx = 1; c.gridy = row; c.weightx = 1; c.fill = GridBagConstraints.HORIZONTAL;
        panel.add(combo, c);

        // Button
        JButton selectBtn = new JButton("Select");
        selectBtn.addActionListener(e -> {
            String chosen = (String) combo.getSelectedItem();
            // Instead of popup → append to text area
            moreArea.append(labelText + " → " + chosen + "\n");
        });
        c = (GridBagConstraints) base.clone();
        c.gridx = 2; c.gridy = row; c.weightx = 0; c.fill = GridBagConstraints.NONE;
        panel.add(selectBtn, c);
    }

    public static void main(String[] args) {
        // Try Nimbus look & feel
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception ignored) {}

        SwingUtilities.invokeLater(() -> new NotificationInterface_1().setVisible(true));
    }
}
